/**
 * Isolated form generation utilities
 */

import { htmxFormGenerator } from '../htmx-generator';
import { ClassificationResult, ScreenClass } from '../types';
import { ExtractionResult } from '../types/form-interfaces';

/**
 * Generate HTMX form markup from FormVisionResult
 * This is an isolated function that can be called independently
 */
export function generateHtmxForm(
  extractionResult: ExtractionResult,
  classificationResult: ClassificationResult,
): string {
  return htmxFormGenerator.generateForm(extractionResult, classificationResult);
}

/**
 * Determine if static description is required
 */
export function needsStaticDescription(extractionResult?: ExtractionResult): boolean {
  if (!extractionResult) {
    return false;
  }

  const keywords = ["password", "email", "username", "phone number", "phone"];

  return extractionResult.controls.fields.some((control) =>
    keywords.some((keyword) =>
      control.label.toLowerCase().includes(keyword)
    )
  );
}